"""
Pydantic models for payroll API parameters and responses.

These models provide comprehensive validation for all parameters
documented in payroll-grid-api-parameters.md and response structures
from PayrollGrid_API_Response_Documentation.md.
"""

from datetime import date
from typing import Any, Dict, List, Optional, Union

from pydantic import BaseModel, Field, field_validator, model_validator


class PayrollGridRow(BaseModel):
    """
    Model for a single payroll grid row record.

    Based on the Row Record Properties section in PayrollGrid_API_Response_Documentation.md
    Updated to match actual API response structure.
    """

    # UI/Display Properties
    id: str = Field(description="Grid row ID (UUID string)")
    iconCls: str = Field(description="CSS class for tree icons")
    children: Optional[List["PayrollGridRow"]] = Field(
        default=None,
        description="Наследници на починалият собственик (recursive structure)",
    )

    # Owner/Contract Information
    owner_id: int = Field(
        description="Уникален идентификатор на собственика в базата данни"
    )
    owner_type: Optional[int] = Field(
        default=None, description="Тип на собственика (1 = individual, other = company)"
    )
    owner_names: str = Field(description="Full name (for individuals) or company name")
    egn_eik: str = Field(
        description="Personal ID (EGN) for individuals or company ID (EIK)"
    )
    first_name: Optional[str] = Field(
        default=None, description="First name (individuals only)"
    )
    surname: Optional[str] = Field(
        default=None, description="Surname (individuals only)"
    )
    lastname: Optional[str] = Field(
        default=None, description="Last name (individuals only)"
    )
    phone: Optional[str] = Field(default=None, description="Phone number")
    mobile: Optional[str] = Field(default=None, description="Mobile phone number")
    iban: Optional[str] = Field(default=None, description="Bank account IBAN")
    rep_names: Optional[str] = Field(
        default=None, description="Имена на представител (HTML formatted)"
    )
    rent_place: Optional[str] = Field(default=None, description="Rental location/place")
    land: Optional[str] = Field(default=None, description="Land/location name")
    is_dead: bool = Field(description="Whether the owner is deceased")
    is_heritor: bool = Field(description="Дали записа е за наследник")

    # Area and Land Data
    area: str = Field(
        alias="owner_area", description="Total area (formatted to 3 decimal places)"
    )
    cultivated_area: str = Field(
        description="Обработваема площ (formatted to 3 decimal places)"
    )
    pu_area: str = Field(
        description="Площ за лично ползване (formatted to 3 decimal places)"
    )
    has_personal_use: Optional[bool] = Field(
        default=None, description="Дали има площ за лично ползване"
    )
    mestnost: str = Field(description="Местност (or '-' if not available)")
    category: str = Field(description="Категория земя (or '-' if not available)")
    c_type: str = Field(alias="contract_type", description="име на типа на договора")
    area_type: str = Field(description="Начин на ползване на земята")

    # Contract Data
    farming: int = Field(alias="farming_id", description="Farming ID")
    contract_array: Optional[List[int]] = Field(
        default=None, description="Array of contract IDs"
    )
    c_num_array: Optional[List[str]] = Field(
        default=None, description="Array of contract numbers"
    )
    sv_num_array: Optional[List[str]] = Field(
        default=None, description="Array of SV numbers"
    )
    sv_num: str = Field(description="SV number (or '-' if not available)")
    sv_date: str = Field(description="SV date (or '-' if not available)")
    plots_array: Optional[List[int]] = Field(
        default=None, description="Array of plot IDs"
    )
    plots_name_array: Optional[List[str]] = Field(
        default=None, description="Array of plot names/identifiers"
    )

    # Financial Data (Money)
    renta: str = Field(description="Реанта в лева (formatted to 2 decimal places)")
    contract_renta: Union[int, float] = Field(description="Raw contract rent amount")
    charged_renta: Optional[str] = Field(
        default=None, description="Начислена рента (formatted or '-')"
    )
    paid_renta: Optional[str] = Field(
        default=None, description="Платена рента (formatted or null)"
    )
    unpaid_renta: str = Field(
        description="Неплатена рента (formatted to 2 decimal places)"
    )
    over_paid: str = Field(
        description="Надплатена рента (formatted to 2 decimal places)"
    )
    converted_renta_nat: Optional[Union[int, float]] = Field(
        default=None, description="Converted natura rent to money value"
    )

    # Natura (In-Kind) Rent Data
    renta_nat: Optional[List[Dict[str, Any]]] = Field(
        default=None, description="Contract rent in natura by type ID"
    )
    renta_nat_text: Optional[str] = Field(
        default=None, description="Форматиран текст на реанта в натура (HTML)"
    )
    charged_renta_nat: Optional[List[Dict[str, Any]]] = Field(
        default=None, description="Начислена рента в натура по ID на типа"
    )
    charged_renta_nat_text: Optional[str] = Field(
        default=None, description="Formatted text of charged rent in natura (HTML)"
    )
    paid_renta_nat: Optional[str] = Field(
        default=None, description="Paid rent in natura (formatted text or '-')"
    )
    paid_renta_nat_details: Optional[List[Dict[str, Any]]] = Field(
        default=None, description="Detailed paid rent in natura by type ID"
    )
    unpaid_renta_nat: Optional[str] = Field(
        default=None, description="Unpaid rent in natura (formatted text)"
    )
    unpaid_renta_nat_arr: Optional[List[Dict[str, Any]]] = Field(
        default=None, description="Неплатена рента в натура по ID на типа"
    )
    over_paid_nat: Optional[str] = Field(
        default=None, description="Overpaid rent in natura (formatted text)"
    )
    over_paid_renta_nat_arr: Optional[List[Dict[str, Any]]] = Field(
        default=None, description="Overpaid rent in natura by type ID"
    )
    renta_nat_type: Optional[str] = Field(
        default=None, description="Types of natura rent (formatted text)"
    )
    unpaid_renta_nat_unit_value: Optional[str] = Field(
        default=None, description="Unit values for unpaid natura rent"
    )

    # Payment Details
    paid_renta_by: Optional[str] = Field(
        default=None, description="В какво е платена рентата  (formatted text or '-')"
    )
    paid_renta_by_arr: Optional[Union[List[Dict[str, Any]], Dict[str, Any]]] = Field(
        default=None, description="Payment methods array (can be dict or list)"
    )
    paid_renta_nat_by: Optional[str] = Field(
        default=None, description="How natura rent was paid (formatted text or '-')"
    )
    paid_renta_nat_by_arr: Optional[Union[List[Dict[str, Any]], Dict[str, Any]]] = (
        Field(
            default=None,
            description="Natura payment methods array (can be dict or list)",
        )
    )
    paid_renta_nat_by_detailed: Optional[str] = Field(
        default=None, description="Detailed natura payments (formatted text or '-')"
    )
    total_by_renta: Optional[str] = Field(
        default=None,
        description="Total payments in money (formatted with ' лв.' or '-')",
    )
    total_by_renta_sum: Optional[Union[int, float]] = Field(
        default=None, description="Raw total payments in money"
    )
    total_by_renta_nat: Optional[str] = Field(
        default=None, description="Total payments in natura (formatted text or '-')"
    )
    total_by_renta_nat_arr: Optional[List[Dict[str, Any]]] = Field(
        default=None, description="Total payments in natura by type"
    )

    # Complex Nested Data Structures
    plots_contracts_area_array: Optional[List[Dict[str, Any]]] = Field(
        default=None,
        description="Plot-contract-area relationships with pc_id, c_num, plot_name, area",
    )
    plots_contracts_renta: Optional[List[Dict[str, Any]]] = Field(
        default=None, description="Rent amounts by plot and contract"
    )
    plots_contracts_charged_renta: Optional[List[Dict[str, Any]]] = Field(
        default=None, description="Charged rent amounts by plot and contract"
    )
    plots_contracts_renta_nat: Optional[List[Dict[str, Any]]] = Field(
        default=None, description="Natura rent by plot, contract, and type"
    )
    plots_contracts_charged_renta_nat: Optional[List[Dict[str, Any]]] = Field(
        default=None, description="Charged natura rent by plot, contract, and type"
    )
    owner_plots_percent: Optional[List[Dict[str, Any]]] = Field(
        default=None, description="Owner percentage by contract ID and plot ID"
    )
    paid_renta_by_contract: Optional[List[Dict[str, Any]]] = Field(
        default=None, description="Paid rent amounts by contract ID"
    )
    paid_renta_nat_by_contract: Optional[List[Dict[str, Any]]] = Field(
        default=None, description="Paid natura rent by contract ID and type"
    )


# Update forward references for recursive model
PayrollGridRow.model_rebuild()


class PayrollGridFooter(BaseModel):
    """
    Model for the payroll grid footer/summary record.

    Based on the Footer Structure section in PayrollGrid_API_Response_Documentation.md
    """

    # Basic Information
    iconCls: str = Field(description="CSS class for display")
    owner_names: str = Field(description="Empty string for totals row")
    rep_names: str = Field(description="Label for totals (e.g., '<b>ОБЩО за стр.</b>')")
    id: Optional[int] = Field(default=None, description="No ID for summary row")

    # Area Data
    all_owner_area: str = Field(
        description="Total area for all owners (formatted to 3 decimal places)"
    )
    owner_area: str = Field(description="Owner area (formatted to 3 decimal places)")
    all_owner_contract_area: str = Field(
        description="Total contract area (formatted to 3 decimal places)"
    )
    cultivated_area: str = Field(
        description="Total cultivated area (formatted to 3 decimal places)"
    )
    pu_area: str = Field(description="Personal use area (or '-' if none)")

    # Financial Data (Money)
    renta: str = Field(
        description="Total contract rent (formatted to 2 decimal places)"
    )
    charged_renta: str = Field(
        description="Total charged rent (formatted to 2 decimal places)"
    )
    paid_renta: str = Field(
        description="Total paid rent (formatted to 2 decimal places)"
    )
    unpaid_renta: str = Field(
        description="Total unpaid rent (formatted to 2 decimal places)"
    )
    over_paid: str = Field(
        description="Total overpaid amount (formatted to 2 decimal places)"
    )
    paid_via_money: Union[int, float] = Field(
        description="Amount paid via money (raw number)"
    )
    all_renta_money: str = Field(
        description="All rent money total (formatted to 2 decimal places)"
    )
    contract_renta: str = Field(
        description="Contract rent total (formatted to 2 decimal places)"
    )
    contract_renta_value: str = Field(
        description="Contract rent value (formatted to 2 decimal places)"
    )
    charged_renta_value: str = Field(
        description="Charged rent value (formatted to 2 decimal places)"
    )

    # Formatted Financial Text (with Currency Conversion)
    renta_txt: str = Field(description="Contract rent with BGN/EUR conversion")
    charged_renta_txt: Optional[str] = Field(
        default=None, description="Charged rent with BGN/EUR conversion"
    )
    paid_renta_txt: str = Field(description="Paid rent with BGN/EUR conversion")
    unpaid_renta_txt: str = Field(description="Unpaid rent with BGN/EUR conversion")
    contract_renta_txt: str = Field(description="Contract rent with BGN/EUR conversion")
    over_paid_txt: str = Field(description="Overpaid amount with BGN/EUR conversion")
    plot_rent_txt: str = Field(description="Plot rent with BGN/EUR conversion")

    # Payment Details
    paid_renta_by: str = Field(
        description="Payment method summary with HTML formatting"
    )
    paid_renta_by_txt: str = Field(description="Payment text with currency conversion")
    paid_renta_by_arr: Dict[str, Any] = Field(
        description="Payment details with amount and nat_amount properties"
    )

    # Natura (In-Kind) Rent Data
    renta_nat_text: str = Field(
        description="Contract natura rent summary (or '-' if none)"
    )
    charged_renta_nat_text: str = Field(
        description="Charged natura rent summary (or '-' if none)"
    )
    paid_renta_nat: str = Field(description="Paid natura rent summary (or '-' if none)")
    paid_renta_nat_by: str = Field(
        description="Paid natura rent by method (or '-' if none)"
    )
    paid_renta_nat_by_detailed: str = Field(
        description="Detailed paid natura rent (or '-' if none)"
    )
    unpaid_renta_nat: str = Field(description="Unpaid natura rent (or '-' if none)")
    unpaid_renta_nat_unit_value: str = Field(
        description="Unit values for unpaid natura rent (or '-' if none)"
    )
    over_paid_nat: str = Field(description="Overpaid natura rent (or '-' if none)")
    renta_nat_type: str = Field(description="Natura rent types")

    # Natura Rent Arrays
    renta_nat: List[Dict[str, Any]] = Field(
        description="Contract natura rent by type (empty if none)"
    )
    charged_renta_nat_with_type: List[Dict[str, Any]] = Field(
        description="Charged natura rent with type info (empty if none)"
    )
    paid_renta_nat_arr: List[Dict[str, Any]] = Field(
        description="Paid natura rent array (empty if none)"
    )
    paid_renta_nat_by_arr: Dict[str, Any] = Field(
        description="Paid natura rent details with amount and nat_amount"
    )
    unpaid_renta_nat_unit_value_arr: List[Dict[str, Any]] = Field(
        description="Unpaid natura rent unit values (empty if none)"
    )
    nat_type_ids: List[int] = Field(description="Natura type IDs (empty if none)")
    renta_nat_type_id: List[int] = Field(
        description="Natura rent type IDs (empty if none)"
    )

    # Additional Properties
    plot_rent: str = Field(
        description="Plot rent amount (formatted to 2 decimal places)"
    )
    rent_place_name: str = Field(
        description="Rent place name (or '-' if not available)"
    )
    category: str = Field(description="Category (or '-' if not available)")
    osz_num: str = Field(description="OSZ number (or '-' if not available)")
    osz_date: str = Field(description="OSZ date (or '-' if not available)")
    over_paid_nat_text: str = Field(description="Overpaid natura text (or '-' if none)")
    unpaid_renta_nat_text: str = Field(
        description="Unpaid natura rent text (or '-' if none)"
    )

    # Personal Use Data
    personal_use_nat_types_names: str = Field(
        description="Personal use natura type names (or '-' if none)"
    )
    personal_use_renta: str = Field(description="Personal use rent (or '-' if none)")
    personal_use_paid_renta: str = Field(
        description="Personal use paid rent (or '-' if none)"
    )
    personal_use_unpaid_renta: str = Field(
        description="Personal use unpaid rent (or '-' if none)"
    )
    personal_use_treatments_sum: str = Field(
        description="Personal use treatments sum (or '-' if none)"
    )
    personal_use_paid_treatments: str = Field(
        description="Personal use paid treatments (or '-' if none)"
    )
    personal_use_unpaid_treatments: str = Field(
        description="Personal use unpaid treatments (or '-' if none)"
    )

    # Internal Calculation Fields
    all_owner_no_rounded: str = Field(description="All owner amounts without rounding")
    all_owner_no_rounded_contract: str = Field(
        description="All owner contract amounts without rounding"
    )


class PayrollParameters(BaseModel):
    """
    Comprehensive model for all payroll grid API parameters.

    Based on the documentation in payroll-grid-api-parameters.md,
    this model includes all possible parameters with proper validation.
    All parameters have default values to ensure robust API requests.
    """

    # Core Parameters (Required)
    type: str = Field(
        default="owners",
        description="Operation mode: 'owners', 'sums', or 'payroll_by_owner'",
        pattern="^(owners|sums|payroll_by_owner)$",
    )
    farming_year: int = Field(
        default=16, description="Farming year ID for payroll calculations", gt=0
    )

    # Date Range Parameters
    payroll_from_date: str = Field(
        default="2024-10-01",
        description="Начална дата за периода на ведомостта (YYYY-MM-DD format)",
        pattern=r"^\d{4}-\d{2}-\d{2}$",
    )
    payroll_to_date: str = Field(
        default="2025-09-30",
        description="Крайна дата за периода на ведомостта (YYYY-MM-DD format)",
        pattern=r"^\d{4}-\d{2}-\d{2}$",
    )

    # Location and Administrative Filters
    payroll_ekate: List[str] = Field(
        default_factory=lambda: [""],
        description="Търсене по ЕКАТТЕ кодове",
    )
    payroll_farming: List[str] = Field(
        default_factory=lambda: [""], description="Търсене по име на стопанство"
    )

    # Owner Type and Identification Filters
    owner_type: str = Field(
        default="0,1",
        description="Owner type filter: '0' (companies), '1' (individuals), '0,1' (both)",
        pattern=r"^(0|1|0,1)$",
    )
    owner_names: str = Field(
        default="", description="Търсене по имена на индивидуален собственик"
    )
    egn: str = Field(default="", description="Търсене по ЕГН номер")
    eik: str = Field(default="", description="Търсене по ЕИК номер")
    company_name: str = Field(default="", description="Търсене по име на компания")

    # Advanced Owner Filters
    owner_egns: List[str] = Field(
        default_factory=list,
        description="Търсене по няколко ЕГН-а номера",
    )
    company_eiks: List[str] = Field(
        default_factory=list,
        description="Търсене по няколко ЕИК-а номера",
    )

    # Representative Filters
    rep_names: str = Field(default="", description="Търсене по имена на представител")
    rep_egn: str = Field(default="", description="Търсене по ЕГН на представител")
    rep_rent_place: str = Field(
        default="",
        description="Търсене по място на получаване на рента от представител",
    )

    # Location Filters
    rent_place: str = Field(
        default="", description="Търсене по място на получаване на рента"
    )

    # Heritor Filters
    heritor_names: str = Field(default="", description="Търсене по имена на наследник")
    heritor_egn: str = Field(default="", description="Търсене по ЕГН на наследник")

    @field_validator("payroll_from_date", "payroll_to_date")
    @classmethod
    def validate_date_format(cls, v: str) -> str:
        """Validate date format and ensure it's a valid date."""
        try:
            # Parse to ensure it's a valid date
            year, month, day = map(int, v.split("-"))
            date(year, month, day)
            return v
        except (ValueError, TypeError) as e:
            raise ValueError(
                f"Invalid date format. Expected YYYY-MM-DD, got: {v}"
            ) from e

    @model_validator(mode="after")
    def validate_date_range(self) -> "PayrollParameters":
        """Ensure payroll_to_date is after payroll_from_date."""
        from_date = date.fromisoformat(self.payroll_from_date)
        to_date = date.fromisoformat(self.payroll_to_date)

        if to_date <= from_date:
            raise ValueError(
                f"payroll_to_date ({self.payroll_to_date}) must be after "
                f"payroll_from_date ({self.payroll_from_date})"
            )

        return self

    @model_validator(mode="after")
    def validate_owner_filters(self) -> "PayrollParameters":
        """Validate owner filter combinations."""
        # If owner_egns is provided and not empty, it overrides egn
        if self.owner_egns and self.egn:
            # Log a warning that egn will be ignored
            pass

        # If company_eiks is provided and not empty, it overrides eik
        if self.company_eiks and self.eik:
            # Log a warning that eik will be ignored
            pass

        return self

    def to_api_params(self) -> Dict[str, Any]:
        """
        Convert to the parameter format expected by the external API.

        This method handles the transformation from the MCP tool parameters
        to the format expected by the payroll grid API. All parameters now
        have default values, so all fields are included in the API request.
        """
        # Include all parameters since they all have default values
        params = self.model_dump()
        return params


class JsonRpcRequest(BaseModel):
    """JSON-RPC 2.0 request model for external API calls."""

    jsonrpc: str = Field(default="2.0", description="JSON-RPC version")
    method: str = Field(description="Method to call")
    params: List[Any] = Field(description="Method parameters")
    id: Union[str, int] = Field(description="Request ID")


class JsonRpcResponse(BaseModel):
    """JSON-RPC 2.0 response model from external API."""

    jsonrpc: str = Field(description="JSON-RPC version")
    id: Union[str, int] = Field(description="Request ID")
    result: Optional[Dict[str, Any]] = Field(None, description="Success result")
    error: Optional[Dict[str, Any]] = Field(None, description="Error details")

    @model_validator(mode="after")
    def validate_result_or_error(self) -> "JsonRpcResponse":
        """Ensure either result or error is present, but not both."""
        if self.result is not None and self.error is not None:
            raise ValueError("Response cannot have both result and error")

        if self.result is None and self.error is None:
            raise ValueError("Response must have either result or error")

        return self


class PayrollGridResponse(BaseModel):
    """
    Model for the payroll grid API response structure.

    Based on PayrollGrid_API_Response_Documentation.md
    """

    rows: List[PayrollGridRow] = Field(description="Array of payroll records")
    total: int = Field(description="Total count of all results")
    footer: List[PayrollGridFooter] = Field(
        description="Array containing summary/totals data"
    )


class ToolResult(BaseModel):
    """Result model for MCP tool responses."""

    success: bool = Field(description="Whether the operation was successful")
    data: Optional[Dict[str, Any]] = Field(None, description="Response data")
    error: Optional[str] = Field(None, description="Error message if failed")

    @classmethod
    def success_result(cls, data: Dict[str, Any]) -> "ToolResult":
        """Create a successful result."""
        return cls(success=True, data=data)

    @classmethod
    def error_result(cls, error: str) -> "ToolResult":
        """Create an error result."""
        return cls(success=False, error=error)
