"""
Comprehensive tests for PayrollGrid Pydantic models against real API response data.

These tests validate that the strongly-typed models can successfully parse
real API response data and catch any discrepancies between the documentation
and actual API responses.
"""

import json
import pytest
from pathlib import Path
from typing import Any, Dict, List
from pydantic import ValidationError

from src.payroll_mcp.models import (
    PayrollGridResponse,
    PayrollGridRow,
    PayrollGridFooter,
)


@pytest.fixture
def api_response_data() -> Dict[str, Any]:
    """Load the real API response data from the test file."""
    test_data_path = Path(__file__).parent / "payroll-api-response.json"
    with open(test_data_path, "r", encoding="utf-8") as f:
        return json.load(f)


@pytest.fixture
def payroll_grid_data(api_response_data) -> Dict[str, Any]:
    """Extract just the payroll grid data from the JSON-RPC response."""
    return api_response_data["result"]


@pytest.fixture
def first_row_data(payroll_grid_data) -> Dict[str, Any]:
    """Get the first row from the payroll grid data."""
    return payroll_grid_data["rows"][0]


@pytest.fixture
def footer_data(payroll_grid_data) -> Dict[str, Any]:
    """Get the footer data from the payroll grid data."""
    return payroll_grid_data["footer"][0]


class TestPayrollGridResponse:
    """Test the main PayrollGridResponse model."""

    def test_parse_full_response(self, payroll_grid_data):
        """
        Test parsing the complete payroll grid response.

        This test is expected to fail initially, revealing discrepancies
        between our model and the actual API response structure.
        """
        try:
            response = PayrollGridResponse(**payroll_grid_data)
            # If we get here, parsing succeeded
            assert response.total == payroll_grid_data["total"]
            assert len(response.rows) == len(payroll_grid_data["rows"])
            assert len(response.footer) == len(payroll_grid_data["footer"])
            print("✓ Full response parsing succeeded!")
        except ValidationError as e:
            # Expected to fail - let's analyze the errors
            print(
                f"\n❌ Full response parsing failed with {len(e.errors())} validation errors:"
            )
            for error in e.errors()[:10]:  # Show first 10 errors
                print(f"  - {error['loc']}: {error['msg']}")
            if len(e.errors()) > 10:
                print(f"  ... and {len(e.errors()) - 10} more errors")

            # Re-raise to mark test as failed but with useful info
            pytest.fail(
                f"PayrollGridResponse validation failed: {len(e.errors())} errors"
            )

    def test_response_structure_analysis(self, payroll_grid_data):
        """Analyze the actual response structure vs our model expectations."""
        actual_keys = set(payroll_grid_data.keys())
        expected_keys = set(PayrollGridResponse.model_fields.keys())

        missing_in_actual = expected_keys - actual_keys
        extra_in_actual = actual_keys - expected_keys

        print(f"\n📊 Response Structure Analysis:")
        print(f"Expected keys: {sorted(expected_keys)}")
        print(f"Actual keys: {sorted(actual_keys)}")

        if missing_in_actual:
            print(f"❌ Missing in actual data: {sorted(missing_in_actual)}")
        if extra_in_actual:
            print(f"➕ Extra in actual data: {sorted(extra_in_actual)}")

        # This should pass as the main structure (rows, total, footer) should be correct
        assert "rows" in actual_keys
        assert "total" in actual_keys
        assert "footer" in actual_keys


class TestPayrollGridRow:
    """Test the PayrollGridRow model."""

    def test_parse_single_row(self, first_row_data):
        """Test parsing a single payroll grid row."""
        try:
            row = PayrollGridRow(**first_row_data)
            print("✓ Single row parsing succeeded!")
            assert row.owner_id == first_row_data["owner_id"]
            assert row.owner_names == first_row_data["owner_names"]
        except ValidationError as e:
            print(
                f"\n❌ Single row parsing failed with {len(e.errors())} validation errors:"
            )
            for error in e.errors()[:15]:  # Show first 15 errors
                print(f"  - {error['loc']}: {error['msg']}")
            if len(e.errors()) > 15:
                print(f"  ... and {len(e.errors()) - 15} more errors")
            pytest.fail(f"PayrollGridRow validation failed: {len(e.errors())} errors")

    def test_row_field_analysis(self, first_row_data):
        """Analyze row fields vs model expectations."""
        actual_keys = set(first_row_data.keys())
        expected_keys = set(PayrollGridRow.model_fields.keys())

        missing_in_actual = expected_keys - actual_keys
        extra_in_actual = actual_keys - expected_keys

        print(f"\n📊 Row Field Analysis:")
        print(f"Expected fields: {len(expected_keys)}")
        print(f"Actual fields: {len(actual_keys)}")

        if missing_in_actual:
            print(
                f"❌ Missing in actual data ({len(missing_in_actual)}): {sorted(list(missing_in_actual)[:10])}"
            )
            if len(missing_in_actual) > 10:
                print(f"    ... and {len(missing_in_actual) - 10} more")

        if extra_in_actual:
            print(
                f"➕ Extra in actual data ({len(extra_in_actual)}): {sorted(list(extra_in_actual)[:10])}"
            )
            if len(extra_in_actual) > 10:
                print(f"    ... and {len(extra_in_actual) - 10} more")

        # Check for some key fields that should exist
        key_fields = ["id", "owner_id", "owner_names", "iconCls"]
        for field in key_fields:
            assert field in actual_keys, f"Key field '{field}' missing from actual data"

    def test_recursive_children_structure(self, first_row_data):
        """Test the recursive children structure."""
        children = first_row_data.get("children", [])
        print(f"\n🔄 Testing recursive children structure:")
        print(f"Parent has {len(children)} children")

        if children:
            # Test first child
            first_child = children[0]
            print(f"First child owner: {first_child.get('owner_names', 'N/A')}")
            print(f"First child is_heritor: {first_child.get('is_heritor', 'N/A')}")

            # Check if child has its own children (deeper nesting)
            grandchildren = first_child.get("children", [])
            print(f"First child has {len(grandchildren)} children")

            if grandchildren:
                # Test deeper nesting
                first_grandchild = grandchildren[0]
                print(
                    f"First grandchild owner: {first_grandchild.get('owner_names', 'N/A')}"
                )

                # Check for great-grandchildren
                great_grandchildren = first_grandchild.get("children", [])
                print(f"First grandchild has {len(great_grandchildren)} children")

        # The structure exists, which is good for our recursive model
        assert isinstance(children, list)

    def test_field_types_validation(self, first_row_data):
        """Test specific field types and values."""
        print(f"\n🔍 Field Type Analysis:")

        # Test some key fields and their types
        test_fields = {
            "id": (str, int),  # Could be either
            "owner_id": int,
            "owner_names": str,
            "is_dead": bool,
            "is_heritor": bool,
            "iconCls": str,
        }

        for field_name, expected_type in test_fields.items():
            if field_name in first_row_data:
                actual_value = first_row_data[field_name]
                actual_type = type(actual_value)

                if isinstance(expected_type, tuple):
                    type_match = actual_type in expected_type
                    expected_type_str = " or ".join(t.__name__ for t in expected_type)
                else:
                    type_match = isinstance(actual_value, expected_type)
                    expected_type_str = expected_type.__name__

                status = "✓" if type_match else "❌"
                print(
                    f"  {status} {field_name}: {actual_type.__name__} (expected {expected_type_str}) = {actual_value}"
                )
            else:
                print(f"  ❓ {field_name}: MISSING")


class TestPayrollGridFooter:
    """Test the PayrollGridFooter model."""

    def test_parse_footer(self, footer_data):
        """Test parsing the footer data."""
        try:
            footer = PayrollGridFooter(**footer_data)
            print("✓ Footer parsing succeeded!")
            assert footer.iconCls == footer_data["iconCls"]
            assert footer.rep_names == footer_data["rep_names"]
        except ValidationError as e:
            print(
                f"\n❌ Footer parsing failed with {len(e.errors())} validation errors:"
            )
            for error in e.errors()[:10]:  # Show first 10 errors
                print(f"  - {error['loc']}: {error['msg']}")
            if len(e.errors()) > 10:
                print(f"  ... and {len(e.errors()) - 10} more errors")
            pytest.fail(
                f"PayrollGridFooter validation failed: {len(e.errors())} errors"
            )

    def test_footer_field_analysis(self, footer_data):
        """Analyze footer fields vs model expectations."""
        actual_keys = set(footer_data.keys())
        expected_keys = set(PayrollGridFooter.model_fields.keys())

        missing_in_actual = expected_keys - actual_keys
        extra_in_actual = actual_keys - expected_keys

        print(f"\n📊 Footer Field Analysis:")
        print(f"Expected fields: {len(expected_keys)}")
        print(f"Actual fields: {len(actual_keys)}")

        if missing_in_actual:
            print(
                f"❌ Missing in actual data ({len(missing_in_actual)}): {sorted(list(missing_in_actual)[:10])}"
            )
        if extra_in_actual:
            print(
                f"➕ Extra in actual data ({len(extra_in_actual)}): {sorted(list(extra_in_actual)[:10])}"
            )

        # Check for key footer fields
        key_fields = ["iconCls", "owner_names", "rep_names"]
        for field in key_fields:
            assert (
                field in actual_keys
            ), f"Key footer field '{field}' missing from actual data"


class TestEdgeCases:
    """Test edge cases and special values."""

    def test_null_and_empty_values(self, first_row_data):
        """Test handling of null and empty values."""
        print(f"\n🔍 Null and Empty Value Analysis:")

        null_fields = []
        empty_string_fields = []
        empty_array_fields = []

        for key, value in first_row_data.items():
            if value is None:
                null_fields.append(key)
            elif value == "":
                empty_string_fields.append(key)
            elif value == []:
                empty_array_fields.append(key)

        print(f"Null fields ({len(null_fields)}): {null_fields[:5]}")
        print(
            f"Empty string fields ({len(empty_string_fields)}): {empty_string_fields[:5]}"
        )
        print(
            f"Empty array fields ({len(empty_array_fields)}): {empty_array_fields[:5]}"
        )

        # These are expected patterns in the data
        assert isinstance(null_fields, list)
        assert isinstance(empty_string_fields, list)
        assert isinstance(empty_array_fields, list)

    def test_dash_placeholder_values(self, first_row_data):
        """Test fields that use '-' as placeholder values."""
        print(f"\n➖ Dash Placeholder Analysis:")

        dash_fields = []
        for key, value in first_row_data.items():
            if value == "-":
                dash_fields.append(key)

        print(f"Fields with '-' placeholder ({len(dash_fields)}): {dash_fields}")

        # Common fields that use '-' as placeholder
        expected_dash_fields = [
            "pu_area",
            "charged_renta",
            "rent_place_name",
            "osz_date",
            "osz_num",
        ]
        for field in expected_dash_fields:
            if field in first_row_data:
                print(f"  {field}: {first_row_data[field]}")


class TestModelDiscrepancyAnalysis:
    """Detailed analysis of discrepancies between models and real API data."""

    def test_detailed_field_mapping_analysis(self, first_row_data):
        """Analyze field mappings between our model and real API data."""
        print(f"\n🔍 Detailed Field Mapping Analysis:")

        # Key field mappings we need to understand
        field_mappings = {
            # Our model field -> Real API field (if different)
            "id": "id",  # Type issue: we expect int, API returns string
            "owner_type": None,  # Missing in API - need to derive or make optional
            "area": "owner_area",  # Different field name
            "has_personal_use": None,  # Missing - need to derive from personal_use arrays
            "c_type": "contract_type",  # Different field name
            "farming": "farming_id",  # Different field name
            "contract_array": None,  # Missing - might be derivable from contract_id
            "sv_num_array": None,  # Missing - might be derivable from sv_num
            "plots_array": None,  # Missing - might be derivable from plot_id
            "plots_name_array": None,  # Missing - might be derivable from kad_ident
            "over_paid": "overpaid_renta",  # Different field name
            "paid_renta_by_arr": "paid_renta_by_arr",  # Type issue: we expect list, API returns dict
        }

        print("Field mapping analysis:")
        for our_field, api_field in field_mappings.items():
            if api_field is None:
                print(f"  ❌ {our_field}: MISSING from API")
            elif api_field in first_row_data:
                our_value = first_row_data[api_field]
                print(
                    f"  ✓ {our_field} -> {api_field}: {type(our_value).__name__} = {str(our_value)[:50]}..."
                )
            else:
                print(f"  ❓ {our_field} -> {api_field}: NOT FOUND")

    def test_type_mismatch_analysis(self, first_row_data):
        """Analyze type mismatches between model expectations and API data."""
        print(f"\n🔧 Type Mismatch Analysis:")

        type_issues = {
            "id": {"expected": int, "actual_field": "id"},
            "paid_renta_by_arr": {
                "expected": list,
                "actual_field": "paid_renta_by_arr",
            },
            "paid_renta_nat_by_arr": {
                "expected": list,
                "actual_field": "paid_renta_nat_by_arr",
            },
            "unpaid_renta_nat_arr": {
                "expected": list,
                "actual_field": "unpaid_renta_nat_arr",
            },
        }

        for field, info in type_issues.items():
            if info["actual_field"] in first_row_data:
                actual_value = first_row_data[info["actual_field"]]
                actual_type = type(actual_value)
                expected_type = info["expected"]

                status = "✓" if isinstance(actual_value, expected_type) else "❌"
                print(
                    f"  {status} {field}: expected {expected_type.__name__}, got {actual_type.__name__}"
                )
                print(f"      Value: {actual_value}")

    def test_missing_required_fields_analysis(self, first_row_data):
        """Analyze which required fields are missing from the API data."""
        print(f"\n❌ Missing Required Fields Analysis:")

        # Fields that are required in our model but missing from API
        missing_required = [
            "owner_type",
            "area",
            "has_personal_use",
            "c_type",
            "farming",
            "contract_array",
            "sv_num_array",
            "plots_array",
            "plots_name_array",
            "converted_renta_nat",
            "charged_renta_nat",
            "paid_renta_nat_details",
            "over_paid_renta_nat_arr",
            "total_by_renta",
            "total_by_renta_sum",
            "total_by_renta_nat",
            "total_by_renta_nat_arr",
            "plots_contracts_area_array",
            "plots_contracts_renta",
            "plots_contracts_charged_renta",
            "plots_contracts_renta_nat",
            "plots_contracts_charged_renta_nat",
            "owner_plots_percent",
            "paid_renta_by_contract",
            "paid_renta_nat_by_contract",
        ]

        print(f"Total missing required fields: {len(missing_required)}")
        for i, field in enumerate(missing_required[:10], 1):
            print(f"  {i}. {field}")
        if len(missing_required) > 10:
            print(f"  ... and {len(missing_required) - 10} more")

    def test_extra_api_fields_analysis(self, first_row_data):
        """Analyze extra fields in API that aren't in our model."""
        print(f"\n➕ Extra API Fields Analysis:")

        # Get fields that exist in API but not in our model
        model_fields = set(PayrollGridRow.model_fields.keys())
        api_fields = set(first_row_data.keys())
        extra_fields = api_fields - model_fields

        print(f"Total extra fields in API: {len(extra_fields)}")

        # Categorize extra fields
        contract_fields = [f for f in extra_fields if "contract" in f.lower()]
        farming_fields = [f for f in extra_fields if "farm" in f.lower()]
        uuid_fields = [f for f in extra_fields if "uuid" in f.lower()]
        date_fields = [f for f in extra_fields if "date" in f.lower()]

        print(f"  Contract-related: {len(contract_fields)} fields")
        print(f"  Farming-related: {len(farming_fields)} fields")
        print(f"  UUID-related: {len(uuid_fields)} fields")
        print(f"  Date-related: {len(date_fields)} fields")

        # Show some examples
        print(f"  Examples: {sorted(list(extra_fields)[:8])}")

    def test_footer_discrepancy_analysis(self, footer_data):
        """Analyze footer-specific discrepancies."""
        print(f"\n📊 Footer Discrepancy Analysis:")

        model_fields = set(PayrollGridFooter.model_fields.keys())
        api_fields = set(footer_data.keys())

        missing_in_api = model_fields - api_fields
        extra_in_api = api_fields - model_fields

        print(f"Footer model fields: {len(model_fields)}")
        print(f"Footer API fields: {len(api_fields)}")
        print(f"Missing in API: {len(missing_in_api)} - {sorted(list(missing_in_api))}")
        print(f"Extra in API: {len(extra_in_api)} - {sorted(list(extra_in_api)[:5])}")

        # The main issue is just one missing field
        assert (
            len(missing_in_api) <= 2
        ), f"Too many missing footer fields: {missing_in_api}"


class TestSummaryAndRecommendations:
    """Summary of findings and recommendations for model updates."""

    def test_comprehensive_summary(self, first_row_data, footer_data):
        """Comprehensive summary of all discrepancies and recommendations."""
        print(f"\n" + "=" * 80)
        print("📋 COMPREHENSIVE ANALYSIS SUMMARY")
        print("=" * 80)

        print(f"\n🎯 KEY FINDINGS:")
        print(
            f"1. Our models are based on documentation that doesn't match the real API"
        )
        print(f"2. The API has 143 fields per row, we modeled 70 fields")
        print(f"3. 25+ required fields in our model are missing from the API")
        print(f"4. 100+ extra fields in the API that we don't have in our model")
        print(f"5. Several type mismatches (string vs int, dict vs list)")
        print(f"6. Footer is mostly correct (only 1 missing field)")

        print(f"\n🔧 CRITICAL ISSUES TO FIX:")
        print(f"1. ID field: API returns string, we expect int")
        print(
            f"2. Field name mismatches: area->owner_area, c_type->contract_type, etc."
        )
        print(f"3. Missing required fields need to be made optional or derived")
        print(f"4. Array fields that are actually dicts: paid_renta_by_arr, etc.")
        print(f"5. Null values in arrays: unpaid_renta_nat_arr can be null")

        print(f"\n💡 RECOMMENDATIONS:")
        print(f"1. Update models to match real API structure, not documentation")
        print(f"2. Make most fields optional with proper defaults")
        print(f"3. Use Union types for fields that can be multiple types")
        print(f"4. Add field aliases for name mismatches")
        print(f"5. Use proper validation for null/empty values")
        print(f"6. Consider creating separate models for different API versions")

        print(f"\n✅ WHAT WORKS CORRECTLY:")
        print(f"1. Basic response structure (rows, total, footer)")
        print(f"2. Recursive children structure")
        print(f"3. Most footer fields")
        print(f"4. Core owner information fields")
        print(f"5. Financial data fields (with name corrections)")

        print(f"\n🚀 NEXT STEPS:")
        print(f"1. Create updated models based on real API structure")
        print(f"2. Use Pydantic field aliases for name mismatches")
        print(f"3. Make fields optional where appropriate")
        print(f"4. Add proper type unions for flexible fields")
        print(f"5. Test with multiple API responses to ensure consistency")

        print("=" * 80)

        # This test always passes - it's just for documentation
        assert True, "Summary completed successfully"
